import express from 'express'
import { GoogleGenerativeAI } from '@google/generative-ai'

const router = express.Router()

// Initialize Google AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!)

// Analyze problem and generate tutorial steps
router.post('/analyze', async (req, res) => {
  try {
    const { problem } = req.body

    if (!problem || typeof problem !== 'string') {
      return res.status(400).json({ 
        error: 'Problem description is required and must be a string' 
      })
    }

    // Get the generative model
    const model = genAI.getGenerativeModel({ model: "gemini-pro" })

    // Create a detailed prompt for tutorial generation
    const prompt = `
You are an expert coding instructor. A user has described a coding problem they need help with. 
Your task is to analyze their problem and create a detailed, step-by-step tutorial plan.

User's Problem: "${problem}"

Please provide a response in the following JSON format:
{
  "title": "Brief title for the tutorial",
  "difficulty": "beginner|intermediate|advanced",
  "estimatedTime": "estimated time in minutes",
  "technologies": ["list", "of", "technologies", "involved"],
  "steps": [
    {
      "stepNumber": 1,
      "title": "Step title",
      "description": "Detailed description of what to do",
      "codeExample": "code snippet if applicable",
      "uiActions": ["list of UI actions like 'click button', 'open file', etc."],
      "expectedResult": "what the user should see after this step"
    }
  ],
  "prerequisites": ["list of things user should know/have"],
  "resources": ["helpful links or documentation"]
}

Make sure the steps are detailed enough to create an animated tutorial showing exactly what buttons to click, what code to write, and what the user should expect to see.
`

    // Generate content
    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()

    // Try to parse the JSON response
    let tutorialPlan
    try {
      // Extract JSON from the response (in case there's extra text)
      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        tutorialPlan = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('No JSON found in response')
      }
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError)
      // Fallback: return the raw text
      tutorialPlan = {
        title: "Custom Tutorial",
        difficulty: "intermediate",
        estimatedTime: "10-15 minutes",
        technologies: ["general"],
        steps: [
          {
            stepNumber: 1,
            title: "AI Analysis",
            description: text,
            codeExample: "",
            uiActions: [],
            expectedResult: "Understanding of the problem"
          }
        ],
        prerequisites: [],
        resources: []
      }
    }

    res.json({
      success: true,
      tutorialPlan,
      metadata: {
        generatedAt: new Date().toISOString(),
        model: "gemini-pro",
        originalProblem: problem
      }
    })

  } catch (error) {
    console.error('AI Analysis Error:', error)
    res.status(500).json({ 
      error: 'Failed to analyze problem',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get tutorial examples
router.get('/examples', (req, res) => {
  const examples = [
    {
      id: 1,
      title: "Add Supabase Authentication",
      problem: "I want to add Supabase authentication to my React app",
      difficulty: "intermediate",
      estimatedTime: "15-20 minutes"
    },
    {
      id: 2,
      title: "Set up Google OAuth",
      problem: "How do I set up Google OAuth in my Next.js project",
      difficulty: "intermediate", 
      estimatedTime: "10-15 minutes"
    },
    {
      id: 3,
      title: "Connect Database to Frontend",
      problem: "I need to connect my database to my frontend",
      difficulty: "beginner",
      estimatedTime: "20-25 minutes"
    },
    {
      id: 4,
      title: "Deploy React App to Vercel",
      problem: "How do I deploy my React app to Vercel",
      difficulty: "beginner",
      estimatedTime: "5-10 minutes"
    }
  ]

  res.json({ examples })
})

export default router
